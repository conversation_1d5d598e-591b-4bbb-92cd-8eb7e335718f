import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { Share2, UserPlus, Crown, AlertCircle, Clock } from "lucide-react";
import { DocumentSharingModal } from "./DocumentSharingModal";
import { DocumentAccessIndicator } from "./DocumentAccessIndicator";
import { PermissionRequestModal } from "./PermissionRequestModal";
import { PendingRequestsModal } from "./PendingRequestsModal";
import { Button } from "./ui/button";
import { Badge } from "./ui/badge";
import { Card, CardContent } from "./ui/card";
import { useToast } from "../hooks/use-toast";

interface DocumentHeaderProps {
  documentId: Id<"documents">;
}

export function DocumentHeader({ documentId }: DocumentHeaderProps) {
  const permission = useQuery(api.sharing.getDocumentPermission, { documentId });
  const document = useQuery(api.documents.getDocument, { id: documentId });
  const userRequest = useQuery(api.sharing.getUserPermissionRequest, { documentId });
  const pendingRequests = useQuery(
    api.sharing.getPendingPermissionRequests, 
    permission?.canShare ? { documentId } : "skip"
  );
  
  const [showSharingModal, setShowSharingModal] = useState(false);
  const [showRequestModal, setShowRequestModal] = useState(false);
  const [showPendingModal, setShowPendingModal] = useState(false);

  const requestPermissionUpgrade = useMutation(api.sharing.requestPermissionUpgrade);
  const { toast } = useToast();

  const handleRequestUpgrade = async (message?: string) => {
    try {
      await requestPermissionUpgrade({ documentId, message });
      toast({
        title: "Request sent",
        description: "Permission request sent to document owner!",
      });
      setShowRequestModal(false);
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send request",
        variant: "destructive",
      });
    }
  };

  return (
    <>
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <h1 className="text-2xl font-semibold text-foreground">
                {document?.title || "Untitled Document"}
              </h1>
              <DocumentAccessIndicator documentId={documentId} />
              
              {/* Owner Information */}
              {permission?.owner && permission.permission !== "owner" && (
                <Badge variant="secondary" className="gap-1">
                  <Crown className="h-3 w-3" />
                  Owned by {permission.owner.name || permission.owner.email}
                </Badge>
              )}
            </div>

            <div className="flex items-center gap-2">
              {/* Request Edit Access Button */}
              {permission?.permission === "read" && !userRequest && (
                <Button
                  onClick={() => setShowRequestModal(true)}
                  className="gap-2"
                  variant="default"
                >
                  <UserPlus className="h-4 w-4" />
                  Request Edit Access
                </Button>
              )}

              {/* Pending Request Status */}
              {userRequest && (
                <Badge variant="secondary" className="gap-1">
                  <Clock className="h-3 w-3" />
                  Request Pending
                </Badge>
              )}

              {/* Pending Requests Notification for Owner */}
              {permission?.canShare && pendingRequests && pendingRequests.length > 0 && (
                <Button
                  onClick={() => setShowPendingModal(true)}
                  variant="destructive"
                  className="gap-2 relative"
                >
                  <AlertCircle className="h-4 w-4" />
                  {pendingRequests.length} Request{pendingRequests.length !== 1 ? 's' : ''}
                  <span className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></span>
                </Button>
              )}

              {/* Share Button */}
              {permission?.canShare && (
                <Button
                  onClick={() => setShowSharingModal(true)}
                  className="gap-2"
                >
                  <Share2 className="h-4 w-4" />
                  Share
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      <DocumentSharingModal
        documentId={documentId}
        isOpen={showSharingModal}
        onClose={() => setShowSharingModal(false)}
      />

      <PermissionRequestModal
        isOpen={showRequestModal}
        onClose={() => setShowRequestModal(false)}
        onSubmit={handleRequestUpgrade}
        ownerName={permission?.owner?.name || permission?.owner?.email || "Document Owner"}
      />

      <PendingRequestsModal
        documentId={documentId}
        isOpen={showPendingModal}
        onClose={() => setShowPendingModal(false)}
      />
    </>
  );
}
