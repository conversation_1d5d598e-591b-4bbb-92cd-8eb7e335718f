import { Authenticated, Unauthenticated, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { SignInForm } from "./SignInForm";
import { SignOutButton } from "./SignOutButton";
import { Toaster } from "sonner";
import { useState } from "react";
import { Id } from "../convex/_generated/dataModel";
import { DocumentList } from "./components/DocumentList";
import { CollaborativeEditor } from "./components/CollaborativeEditor";
import { PresenceIndicator } from "./components/PresenceIndicator";
import { FileText, Users } from "lucide-react";

export default function App() {
  return (
    <div className="h-screen flex flex-col bg-gray-50 overflow-hidden">
      <header className="sticky top-0 z-10 py-3 bg-white/80 backdrop-blur-sm h-16 flex justify-between items-center border-b shadow-sm px-4">
        <div className="flex items-center gap-2">
          <FileText className="text-blue-600" size={24} />
          <h2 className="text-xl font-semibold text-gray-900">Collaborative Editor</h2>
        </div>
        <SignOutButton />
      </header>
      <main className="flex-1 flex">
        <Content />
      </main>
      <Toaster />
    </div>
  );
}

function Content() {
  const loggedInUser = useQuery(api.auth.loggedInUser);
  const [selectedDocumentId, setSelectedDocumentId] = useState<Id<"documents"> | undefined>();

  if (loggedInUser === undefined) {
    return (
      <div className="flex-1 flex justify-center items-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <>
      <Unauthenticated>
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="w-full max-w-md mx-auto text-center">
            <div className="mb-8">
              <FileText size={64} className="mx-auto mb-4 text-blue-600" />
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Collaborative Editor
              </h1>
              <p className="text-gray-600">
                Create and edit documents together in real-time
              </p>
            </div>
            <SignInForm />
          </div>
        </div>
      </Unauthenticated>

      <Authenticated>
        <div className="flex-1 flex h-full overflow-hidden">
          <DocumentList
            onSelectDocument={(id) => setSelectedDocumentId(id as Id<"documents">)}
            selectedDocumentId={selectedDocumentId}
          />

          <div className="flex-1 flex flex-col bg-gray-50">
            {selectedDocumentId ? (
              <>
                <PresenceIndicator roomId={selectedDocumentId} />
                <div className="flex-1 p-8">
                  <div className="max-w-4xl mx-auto">
                    <CollaborativeEditor documentId={selectedDocumentId} />
                  </div>
                </div>
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center p-8">
                <div className="text-center text-gray-500 max-w-md">
                  <FileText size={64} className="mx-auto mb-6 opacity-50" />
                  <h2 className="text-2xl font-semibold mb-3 text-gray-900">Welcome to Collaborative Editor</h2>
                  <p className="mb-6 text-gray-600">Select a document from the sidebar to start editing</p>
                  <div className="flex items-center justify-center gap-6 text-sm">
                    <div className="flex items-center gap-2">
                      <Users size={18} className="text-blue-600" />
                      <span>Real-time collaboration</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <FileText size={18} className="text-blue-600" />
                      <span>Rich text editing</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </Authenticated>
    </>
  );
}
